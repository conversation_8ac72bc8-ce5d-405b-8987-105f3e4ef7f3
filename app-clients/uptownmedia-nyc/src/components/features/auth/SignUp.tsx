"use client";
import type React from "react";

import { <PERSON><PERSON>, Card, CardBody, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { getProviders, signIn } from "next-auth/react";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function SignUp() {
	const [availableProviders, setAvailableProviders] = useState<string[]>([]);
	const [email, setEmail] = useState("");
	const [firstName, setFirstName] = useState("");
	const [lastName, setLastName] = useState("");

	useEffect(() => {
		const loadProviders = async () => {
			const providers = await getProviders();

			if (providers) {
				setAvailableProviders(Object.keys(providers));
			}
		};

		loadProviders();
	}, []);

	const handleEmailSignUp = (e: React.FormEvent) => {
		e.preventDefault();
		signIn("credentials", {
			email,
			firstName,
			lastName,
			redirect: true,
		});
	};

	return (
		<div className="flex items-center justify-center min-h-screen p-4 bg-gray-100">
			<Card className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
				<CardBody className="space-y-6">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900">Sign up</h1>
					</div>
					<form className="space-y-4" onSubmit={handleEmailSignUp}>
						<div>
							<label
								className="block mb-1 text-sm font-medium text-gray-700"
								htmlFor="firstName"
							>
								First Name
							</label>
							<Input
								required
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								id="firstName"
								type="text"
								value={firstName}
								onChange={(e) => setFirstName(e.target.value)}
							/>
						</div>

						<div>
							<label
								className="block mb-1 text-sm font-medium text-gray-700"
								htmlFor="lastName"
							>
								Last Name
							</label>
							<Input
								required
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								id="lastName"
								type="text"
								value={lastName}
								onChange={(e) => setLastName(e.target.value)}
							/>
						</div>

						<div>
							<label
								className="block mb-1 text-sm font-medium text-gray-700"
								htmlFor="email"
							>
								Email
							</label>
							<Input
								required
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								id="email"
								placeholder="<EMAIL>"
								type="email"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
							/>
						</div>

						<Button
							className="w-full text-white bg-black hover:bg-gray-800"
							size="lg"
							type="submit"
						>
							Continue with email
						</Button>
					</form>

					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t border-gray-300" />
						</div>
						<div className="relative flex justify-center text-sm">
							<span className="px-2 text-gray-500 bg-white">or</span>
						</div>
					</div>
					<div className="space-y-3">
						{availableProviders.includes("google") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:google-icon" />
								}
								variant="bordered"
								onPress={() => signIn("google")}
							>
								Continue with Google
							</Button>
						)}

						{availableProviders.includes("facebook") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:facebook" />
								}
								variant="bordered"
								onPress={() => signIn("facebook")}
							>
								Continue with Facebook
							</Button>
						)}

						{availableProviders.includes("apple") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={<Icon className="text-xl" icon="logos:apple" />}
								variant="bordered"
								onPress={() => signIn("apple")}
							>
								Continue with Apple
							</Button>
						)}
					</div>
					<div className="text-center">
						<p className="text-xs text-gray-500">
							By clicking "Create account" above, you acknowledge that you will
							receive updates from the Resume team and that you have read,
							understood, and agree to Resume Library's
							<Link
								className="text-blue-600 hover:text-blue-500"
								href="/terms-service"
							>
								Terms & Conditions
							</Link>
							,{" "}
							<Link
								className="text-blue-600 hover:text-blue-500"
								href="/privacy-policy"
							>
								LinkedIn Agreement
							</Link>{" "}
							and{" "}
							<Link
								className="text-blue-600 hover:text-blue-500"
								href="/privacy-policy"
							>
								Privacy Policy
							</Link>
							.
						</p>
					</div>
					<div className="text-center">
						<p className="text-sm text-gray-600">
							Already have an account?{" "}
							<Link
								className="font-medium text-blue-600 hover:text-blue-500"
								href="/auth/sign-in"
							>
								Log in
							</Link>
						</p>
					</div>
				</CardBody>
			</Card>
		</div>
	);
}
