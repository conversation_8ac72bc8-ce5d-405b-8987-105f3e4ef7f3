"use client";

import type { GroupMembershipResponse } from "@/app/api/group-memberships/dto/groupMembershipResponse";
import type { GroupResponse } from "@/app/api/groups/dto/groupResponse";

import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import {
	Checkbox,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
} from "@heroui/react";
import { useSession } from "next-auth/react";
import React, { useEffect, useState, useCallback } from "react";

import { UserRole } from "@/app/api/auth/users/dao/user";
import ContentWrapperBase from "@/components/shared/ContentWrapperBase";
import { getGroupMembershipsByEditorAndGroupType } from "@/services/groupServices";
import usePortalStore from "@/stores/usePortalStore";
import { fetchClient } from "@/utils/fetchClient";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";



function GroupMemberships({ groupType }: { groupType: string }) {
	const [inviteEmail, setInviteEmail] = useState("");
	const generatedInvitationLink =
		"https://www.msgmason.com/invitation?q=team+members=c...";
	const user = usePortalStore((state) => state.user);
	const session = useSession();
	// const currentWorkspace = useAppStore((state) => state.selectedWorkspace);
	const [userGroup, setUserGroup] = useState<GroupResponse>();
	const [groupMembers, setGroupMembers] = useState<GroupMembershipResponse[]>(
		[],
	);
	const [isLoadingMembers, setIsLoadingMembers] = useState(false);
	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "common");
	const [isLoading, setIsLoading] = useState(true);
	const [processingMemberId, setProcessingMemberId] = useState<string | null>(
		null,
	);

	// Function to fetch group members
	const fetchGroupMembers = useCallback(async (sharedPasscode: string) => {
		setIsLoadingMembers(true);
		try {
			const response = await fetchClient<{
				memberships: GroupMembershipResponse[];
			}>(`/groups/${sharedPasscode}/members`);

			console.log("Group members response:", response);
			setGroupMembers(response.memberships || []);
		} catch (error) {
			console.error("Error fetching group members:", error);
			setGroupMembers([]);
		} finally {
			setIsLoadingMembers(false);
		}
	}, []);

	if (session.status === "unauthenticated") return <div>unauthenticated</div>;
	useEffect(() => {
		console.log(session);
		if (session.data?.user?.id) {
			getGroupMembershipsByEditorAndGroupType(session.data?.user?.id, "")
				.then((response) => {
					const group = response[0];

					setUserGroup(group);
					console.log("Group memberships:", response);

					if (group?.sharedPasscode) {
						console.log(
							"Fetching members for group with passcode:",
							group.sharedPasscode,
						);
						fetchGroupMembers(group.sharedPasscode);
					} else {
						console.log("No group or shared passcode found:", group);
					}

					setIsLoading(false);
				})
				.catch((error) => {
					console.error("Error fetching group memberships:", error);
					setUserGroup({} as GroupResponse);
					setGroupMembers([]);
					setIsLoading(false);
				});
		}
	}, [session, fetchGroupMembers]);

	if (isLoading) {
		return <div>Loading...</div>;
	}

	const isEditor = user?.roles?.includes(UserRole.EDITOR) || false;
	const hasGroup = userGroup && Object.keys(userGroup).length > 0;

	console.log("User group:", userGroup);
	console.log("Group members:", groupMembers);
	console.log("Is loading members:", isLoadingMembers);

	const handleCopyLink = () => {
		navigator.clipboard.writeText(generatedInvitationLink);
	};

	const handleCopyGroupCode = () => {
		if (userGroup?.sharedPasscode) {
			navigator.clipboard.writeText(userGroup.sharedPasscode);
		}
	};

	const handleApproveMember = async (userId: string) => {
		if (!userGroup?.sharedPasscode) return;

		setProcessingMemberId(userId);
		try {
			const response = await fetch(
				`/api/groups/${userGroup.sharedPasscode}/members/${userId}/approve`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				// Refresh the group members list
				await fetchGroupMembers(userGroup.sharedPasscode);
			} else {
				console.error("Failed to approve member");
			}
		} catch (error) {
			console.error("Error approving member:", error);
		} finally {
			setProcessingMemberId(null);
		}
	};

	const handleInvite = () => {
		// Handle invitation logic
		setInviteEmail("");
	};



	return (
		<ContentWrapperBase
			className="border-none"
			title={t(`${groupType}-memberships`)}
		>
			<div className="p-6 ">
				<div className="space-y-8">
					{isEditor && hasGroup && (
						<section className="p-6 border rounded-lg bg-gray-50">
							<h2 className="mb-4 text-xl font-semibold">Your Group</h2>
							<div className="space-y-4">
								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<div>
										<h3 className="text-lg font-semibold">
											{userGroup?.groupName}
										</h3>
										<p className="text-default-500">
											Type: {userGroup?.groupType}
										</p>
									</div>
									<div>
										<label
											className="block mb-2 text-sm font-medium"
											htmlFor="group-code-input"
										>
											Group Code
										</label>
										<div className="flex gap-2">
											<Input
												readOnly
												className="flex-1 text-black"
												id="group-code-input"
												value={userGroup?.sharedPasscode}
											/>
											<Button
												className="text-white"
												color="primary"
												onPress={handleCopyGroupCode}
											>
												<span className="flex items-center gap-2">
													{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
													<svg
														fill="none"
														height="20"
														viewBox="0 0 24 24"
														width="20"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path
															d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
															stroke="currentColor"
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth="2"
														/>
													</svg>
													Copy Code
												</span>
											</Button>
										</div>
										<p className="mt-1 text-xs text-default-500">
											Share this code with team members to join your group
										</p>
									</div>
								</div>
							</div>
						</section>
					)}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-link")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("share-link-with-members")}
						</p>
						<div className="flex gap-2">
							<Input
								readOnly
								className="flex-1"
								value={generatedInvitationLink}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleCopyLink}
							>
								<span className="flex items-center gap-2">
									{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
									<svg
										fill="none"
										height="20"
										viewBox="0 0 24 24"
										width="20"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
											stroke="currentColor"
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
										/>
									</svg>
									{t("copy-link")}
								</span>
							</Button>
						</div>
					</section>

					{/* Invite by email section */}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-email")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("email-invite-validity")}
						</p>
						<div className="flex gap-2">
							<Input
								className="flex-1"
								placeholder={t("enter-email-placeholder")}
								type="email"
								value={inviteEmail}
								onChange={(e) => setInviteEmail(e.target.value)}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleInvite}
							>
								{t("invite")}
							</Button>
						</div>
						<p className="mt-1 text-sm text-default-500">
							{t("send-invitation-one-at-time")}
						</p>
					</section>

					{/* Group memberships list */}
					<section className="border rounded-lg">
						<div className="flex items-center justify-between p-4 border-b">
							<div className="flex items-center gap-2">
								<Checkbox />
								<h3 className="text-lg font-semibold">
									{t("team-memberships")}
								</h3>
							</div>
							<Dropdown>
								<DropdownTrigger>
									<Button className="min-w-unit-24" variant="light">
										{t("actions")}
										{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
										<svg
											fill="none"
											height="16"
											viewBox="0 0 24 24"
											width="16"
											xmlns="http://www.w3.org/2000/svg"
										>
											<path
												d="M6 9L12 15L18 9"
												stroke="currentColor"
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
											/>
										</svg>
									</Button>
								</DropdownTrigger>
								<DropdownMenu aria-label="Membership actions">
									<DropdownItem key={"action-remove"}>
										{t("remove")}
									</DropdownItem>
									<DropdownItem key={"action-approve"}>
										{t("approve")}
									</DropdownItem>
								</DropdownMenu>
							</Dropdown>
						</div>

						<div className="divide-y">
							{isLoadingMembers ? (
								<div className="p-4 text-center text-default-500">
									Loading members...
								</div>
							) : groupMembers.length > 0 ? (
								groupMembers.map((membership, idx) => (
									<div
										key={`group-${membership?.id}-${idx}`}
										className="flex items-center justify-between p-4"
									>
										<div className="flex items-center gap-4">
											<Checkbox isDisabled={membership.userId === user?.id} />
											<span>{membership?.user?.name}</span>
										</div>
										<div className="flex items-center gap-2">
											{membership.approved ? (
												<span className="px-3 py-1 text-sm text-green-700 bg-green-100 rounded-full">
													{t("approved")}
												</span>
											) : (
												<>
													<Button
														color="success"
														isDisabled={processingMemberId !== null}
														isLoading={processingMemberId === membership.userId}
														size="sm"
														variant="flat"
														onPress={() =>
															handleApproveMember(membership.userId)
														}
													>
														{t("approve")}
													</Button>
													{/* <Button
														size="sm"
														color="danger"
														variant="flat"
														isDisabled={true}
														onPress={() => handleRejectMember(membership.user_id)}
													>
														{t("reject")}
													</Button> */}
												</>
											)}
										</div>
									</div>
								))
							) : (
								<div className="p-4 text-center text-default-500">
									No members found
								</div>
							)}
						</div>
					</section>
				</div>
			</div>


		</ContentWrapperBase>
	);
}

export default GroupMemberships;
