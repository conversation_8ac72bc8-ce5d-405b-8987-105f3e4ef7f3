"use client";
import { But<PERSON> } from "@heroui/react";
import { Input } from "@heroui/input";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { use } from "react";

import { getGroupMembershipsByEditorAndGroupType } from "@/services/groupServices";

export default function ArticlesPage({
	params,
}: {
	params: Promise<{ role: "admin" | "author" | "editor" | "guest" }>;
}) {
	const { role } = use(params);
	const { data: session, status } = useSession();
	const router = useRouter();
	const [articles, setArticles] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [userGroup, setUserGroup] = useState<any>(null);
	const [groupCodeCopied, setGroupCodeCopied] = useState(false);

	useEffect(() => {
		const fetchData = async () => {
			try {
				// Fetch articles based on role
				let articlesEndpoint = "/api/articles";
				if (role === "editor") {
					articlesEndpoint = "/api/articles/editor";
				}

				const articlesResponse = await fetch(articlesEndpoint);
				if (articlesResponse.ok) {
					const articlesData = await articlesResponse.json();
					setArticles(articlesData.articles || []);
				}

				// Fetch group data for editors
				if (role === "editor" && session?.user?.id) {
					try {
						const groups = await getGroupMembershipsByEditorAndGroupType(
							session.user.id,
							"",
						);
						if (groups && groups.length > 0) {
							setUserGroup(groups[0]);
						}
					} catch (error) {
						console.error("Error fetching group data:", error);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			} finally {
				setIsLoading(false);
			}
		};

		if (session?.user?.id) {
			fetchData();
		}
	}, [role, session?.user?.id]);

	const handleCopyGroupCode = async () => {
		if (userGroup?.sharedPasscode) {
			try {
				await navigator.clipboard.writeText(userGroup.sharedPasscode);
				setGroupCodeCopied(true);
				setTimeout(() => setGroupCodeCopied(false), 2000);
			} catch (error) {
				console.error("Failed to copy group code:", error);
			}
		}
	};

	if (status === "loading") {
		return <div className="p-8 text-center">Loading...</div>;
	}

	if (status === "unauthenticated") {
		router.push("/auth/sign-in");

		return null;
	}

	return (
		<div className="max-w-4xl p-6 mx-auto">
			<div className="flex items-center justify-between mb-8">
				<h1 className="text-2xl font-bold">Articles</h1>
				{(role === "author" || role === "admin") && (
					<Link href={`/portal/${role}/articles/create`}>
						<Button color="primary">Create New Article</Button>
					</Link>
				)}
			</div>

			{/* Group Code Display for Editors */}
			{role === "editor" && userGroup?.sharedPasscode && (
				<div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<div className="flex items-center justify-between">
						<div>
							<h3 className="text-sm font-medium text-blue-800">
								Your Group Code
							</h3>
							<p className="text-xs text-blue-600 mt-1">
								Share this code with authors to invite them to your group
							</p>
						</div>
						<div className="flex items-center gap-2">
							<Input
								readOnly
								className="w-40 text-sm font-mono"
								value={userGroup.sharedPasscode}
								variant="bordered"
								size="sm"
							/>
							<Button
								size="sm"
								variant="flat"
								color="primary"
								onPress={handleCopyGroupCode}
							>
								{groupCodeCopied ? "Copied!" : "Copy"}
							</Button>
						</div>
					</div>
				</div>
			)}

			{isLoading ? (
				<div className="p-4 text-center">Loading articles...</div>
			) : articles.length > 0 ? (
				<div className="space-y-4">
					{articles.map(
						(article: {
							id: string;
							title: string;
							description: string;
							topic: string;
							published: string;
						}) => (
							<div
								key={article.id}
								className="p-4 transition-shadow border rounded-md shadow-sm hover:shadow-md"
							>
								<h2 className="text-xl font-semibold">{article.title}</h2>
								<p className="text-gray-600 line-clamp-2">
									{article.description}
								</p>
								<div className="flex items-center justify-between mt-2">
									<span className="text-sm text-gray-500">
										Topic: {article.topic}
									</span>
									<span className="text-sm text-gray-500">
										Published:{" "}
										{new Date(article.published).toLocaleDateString()}
									</span>
								</div>
							</div>
						),
					)}
				</div>
			) : (
				<div className="p-8 text-center text-gray-500">
					No articles found.{" "}
					{role === "author" && "Create your first article!"}
					{role === "editor" &&
						"Articles submitted by authors in your group will appear here."}
					{role === "admin" && "Create your first article!"}
				</div>
			)}
		</div>
	);
}
