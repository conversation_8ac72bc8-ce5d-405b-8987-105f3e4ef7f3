"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { description, subtitle, title } from "@/components/primitives";
import uLogo from "../../../../public/assets/u-logo.png";

const formSchema = z.object({
	groupCode: z.string().min(1, { message: "Group code is required" }),
	phoneNumber: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function RequestToJoin() {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const [errorMessage, setErrorMessage] = useState("");
	const [userName, setUserName] = useState("");
	const [editorName, setEditorName] = useState("");
	const [isLoading, setIsLoading] = useState(true);

	const { data: session, status } = useSession({
		required: true,
		onUnauthenticated() {
			router.push("/auth/sign-in");
		},
	});

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
	});

	useEffect(() => {
		if (status === "loading") {
			setIsLoading(true);
		} else {
			setIsLoading(false);
			if (status === "authenticated") {
				if (session?.user?.name) {
					setUserName(session.user.name);
				} else if (session?.user?.email) {
					setUserName(session.user.email.split("@")[0]);
				}
			}
		}
	}, [session, status]);

	const onSubmit = async (data: FormValues) => {
		setIsSubmitting(true);
		setErrorMessage("");

		try {
			// Create the membership request
			const membershipData = {
				userId: session?.user?.id || session?.user?.email || "unknown",
				phoneNumber: data.phoneNumber || undefined,
			};

			const response = await fetch(`/api/groups/${data.groupCode}/members`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(membershipData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to submit request");
			}

			const result = await response.json();

			// Try to get group information to get editor name
			try {
				const groupInfoResponse = await fetch(
					`/api/groups/${data.groupCode}/members`,
				);
				if (groupInfoResponse.ok) {
					console.log(groupInfoResponse)
					// For now, we'll set a placeholder editor name
					// In a real implementation, we would get this from the group info
					setEditorName(result?.user?.name); // This should come from the API response
				}
			} catch (groupError) {
				console.warn("Could not fetch group info:", groupError);
				setEditorName("Editor");
			}

			console.log("Form data submitted:", data);
			console.log("Response:", result);
			setSubmitSuccess(true);
		} catch (error) {
			console.error("Error submitting request:", error);
			setErrorMessage(
				error instanceof Error
					? error.message
					: "An error occurred while submitting your request. Please try again.",
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center w-full min-h-screen p-4">
				<div className="relative">
					<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
						<Link className="flex items-center justify-center" href="/">
							<Image
								alt="Uptown Media Logo"
								className="object-cover"
								src={uLogo}
							/>
						</Link>
					</div>
					<Card className="w-full max-w-[545px] shadow-lg">
						<CardBody className="flex items-center justify-center gap-4 p-10">
							<div className="text-center">
								<h2 className={subtitle({ size: "lg" })}>Loading...</h2>
								<p className={description({ size: "sm", className: "mt-2" })}>
									Please wait while we load your information.
								</p>
							</div>
						</CardBody>
					</Card>
				</div>
			</div>
		);
	}

	if (submitSuccess) {
		return (
			<div className="flex items-center justify-center w-full min-h-screen p-4">
				<div className="relative">
					<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
						<Link className="flex items-center justify-center" href="/">
							<Image
								alt="Uptown Media Logo"
								className="object-cover"
								src={uLogo}
							/>
						</Link>
					</div>
					<Card className="w-full max-w-[545px] shadow-lg">
						<CardBody className="gap-4 p-10">
							<div className="flex flex-col items-center">
								<div className="text-center">
									<h1 className={title({ size: "md", className: "font-bold" })}>
										Request to Join
									</h1>
									<p className={title({ size: "sm", className: "mt-2 mb-4" })}>
										Hello {userName}
									</p>
									<p className={description({ size: "sm", className: "mt-4" })}>
										Request has been submitted to
										<br />
										<span className="font-semibold uppercase">
											{editorName || "EDITOR"}
										</span>
									</p>
									<p className={description({ size: "sm", className: "mt-4" })}>
										You will receive an email once access is granted
									</p>
								</div>
							</div>
						</CardBody>
					</Card>
				</div>
			</div>
		);
	}

	return (
		<div className="flex items-center justify-center w-full min-h-screen p-4">
			<div className="relative">
				<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
					<Link className="flex items-center justify-center" href="/">
						<Image
							alt="Uptown Media Logo"
							className="object-cover"
							src={uLogo}
						/>
					</Link>
				</div>
				<Card className="w-full max-w-3xl shadow-lg md:px-20">
					<CardBody className="gap-2 p-10">
						<h1 className={title({ size: "lg", className: "text-center" })}>
							Request to Join
						</h1>

						<p className={subtitle({ size: "sm", className: "text-center" })}>
							Hello {userName}
						</p>
						<p
							className={description({ size: "sm", className: "text-center" })}
						>
							Please provide the following required information
						</p>

						{errorMessage && (
							<div className="p-3 mb-4 text-sm text-white bg-red-500 rounded">
								{errorMessage}
							</div>
						)}

						<form
							onSubmit={handleSubmit(onSubmit)}
							className="flex flex-col gap-4"
						>
							<div className="flex flex-col">
								<label htmlFor="groupCode" className="mb-1 text-sm">
									Group Code <span className="text-red-500">*</span>
								</label>
								<input
									id="groupCode"
									type="text"
									className={`p-1.5 border rounded ${errors.groupCode ? "border-red-500" : "border-gray-300"}`}
									placeholder="Enter group code"
									{...register("groupCode")}
								/>
								{errors.groupCode && (
									<span className="mt-1 text-xs text-red-500">
										{errors.groupCode.message}
									</span>
								)}
							</div>

							<div className="flex flex-col">
								<label htmlFor="phoneNumber" className="mb-1 text-sm">
									Phone Number (Optional)
								</label>
								<input
									id="phoneNumber"
									type="tel"
									className={`p-1.5 border rounded ${errors.phoneNumber ? "border-red-500" : "border-gray-300"}`}
									placeholder="Phone Number (Optional)"
									{...register("phoneNumber")}
								/>
								{errors.phoneNumber && (
									<span className="mt-1 text-xs text-red-500">
										{errors.phoneNumber.message}
									</span>
								)}
							</div>

							<Button
								type="submit"
								className="w-full mt-4 text-white bg-black"
								isLoading={isSubmitting}
								isDisabled={isSubmitting}
							>
								Submit Request
							</Button>
						</form>
					</CardBody>
				</Card>
			</div>
		</div>
	);
}
