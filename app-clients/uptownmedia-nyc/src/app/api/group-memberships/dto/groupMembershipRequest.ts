import { z } from "zod";

export const GroupMembershipRequestSchema = z.object({
	userId: z.string().min(1, "user_id is required"),
	phoneNumber: z.string().optional(),
	approved: z.boolean().optional(),
	approvedAt: z.number().optional(),
	user: z.object({
		id: z.string().min(1, "user_id is required"),
		name: z.string().min(1, "user_name is required"),
		email: z.string().email().min(1, "user_email is required"),
	}).optional(),
});

export type GroupMembershipRequest = z.infer<
	typeof GroupMembershipRequestSchema
>;
