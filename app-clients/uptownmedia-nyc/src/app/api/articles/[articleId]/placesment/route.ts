import { NextResponse } from "next/server";

import { ArticleService } from "../../services/articleServices";

import { auth } from "@/auth";
const articleServices = new ArticleService();

export const PUT = auth(async function PUT(
	request,
	props: { params: Promise<{ articleId: string }> },
) {
	const params = await props.params;
	const body = await request.json();

	if (!request.auth)
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	const user = request.auth.user;

	if (!user) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	await articleServices.setPlacement(params.articleId, body.placement);

	return NextResponse.json(
		{
			message: "Article set Placement",
		},
		{ status: 200 },
	);
});
