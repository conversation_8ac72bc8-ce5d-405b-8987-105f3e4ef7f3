import type { ArticleRequest } from "../dto/articleRequest";

import { NotFoundError } from "../../shared/errors";
import { Article } from "../dao/article";
import { ArticleDynamoDBRepository } from "../repository/articleRepository";

export class ArticleService {
	private repository: ArticleDynamoDBRepository;
	constructor() {
		this.repository = new ArticleDynamoDBRepository();
	}

	// CREATE
	public async createArticle(request: ArticleRequest, userId: string) {
		const {
			title,
			description,
			author,
			topic,
			published,
			image,
			featured,
			approved,
			approvedBy,
			placement,
		} = request;

		const article = new Article(
			title,
			description,
			author,
			topic,
			published,
			image,
			false, // Default to false for new articles
			"", // Default to false for new articles
			"",
			0,
			userId,
		);

		await this.repository.saveArticle(article);
	}

	public async getAllArticles() {
		const listOfArticles = await this.repository.getAllArticles();

		return listOfArticles;
	}

	public async getArticleById(id: string) {
		const article = await this.repository.getArticleById(id);

		if (!article) {
			throw new NotFoundError(`Article with ID ${id} not found`);
		}

		return article;
	}
	public async getArticlesByUserId(userId: string) {
		const articles = await this.repository.getArticlesByUserId(userId);

		return articles;
	}
	public async approveArticle(articleId: string, userId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		const date = new Date();

		article.setApproved(date.toISOString());
		article.setApprovedBy(userId);
		console.log("approving article", article);
		await this.repository.saveArticle(article);
	}
}
