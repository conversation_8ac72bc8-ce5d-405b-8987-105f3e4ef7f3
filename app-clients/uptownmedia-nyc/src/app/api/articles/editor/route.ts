import { UserServices } from "../../auth/users/services/userServices";
import { GroupMembershipServices } from "../../group-memberships/services/groupMembershipsServices";
import { GroupServices } from "../../groups/services/groupsServices";
import { ArticleService } from "../services/articleServices";

import { auth } from "@/auth";

const articleServices = new ArticleService();
const userServices = new UserServices();
const groupServices = new GroupServices();
const memberServices = new GroupMembershipServices();

export async function GET() {
	const session = await auth();
	const userId = session?.user.id as string;
	const group = await groupServices.getGroupByEditorId(userId);

	console.log("Group for editor:", group);
	const member = await memberServices.getGroupMembershipsByGroupId(
		group.getGroupId(),
	);

	console.log("Member for editor:", member);
	const articleListPromise = member.map(
		async (item) => await articleServices.getArticlesByUserId(item.getUserId()),
	);

	const articles = await Promise.all(articleListPromise);

	console.log("Article list promise:", articles);

	return Response.json({ articles }, { status: 200 });
}
