import { ArticleService } from "../services/articleServices";

import { auth } from "@/auth";

const articleServices = new ArticleService();

export async function GET() {
	try {
		const session = await auth();
		const userId = session?.user.id as string;

		if (!userId) {
			return Response.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Use the new service method to get articles for editor groups
		const articles = await articleServices.getArticlesByEditorGroups(userId);

		return Response.json({ articles }, { status: 200 });
	} catch (error) {
		console.error("Error fetching articles for editor:", error);
		return Response.json(
			{ error: "Failed to fetch articles" },
			{ status: 500 }
		);
	}
}
