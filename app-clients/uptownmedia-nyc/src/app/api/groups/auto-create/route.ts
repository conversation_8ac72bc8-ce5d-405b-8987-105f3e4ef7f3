import { z } from "zod";

import { AppError } from "../../shared/errors";
import { GroupServices } from "../services/groupsServices";

import { auth } from "@/auth";

const groupServices = new GroupServices();

// Schema for auto-create request
const AutoCreateGroupSchema = z.object({
	editorId: z.string(),
});

function getRandomInt(min: number, max: number): number {
	const range = max - min;
	const randomBuffer = new Uint32Array(1);

	crypto.getRandomValues(randomBuffer);

	return min + (randomBuffer[0] % range);
}

function randomLetters(length: number): string {
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

	return Array.from({ length }, () =>
		letters.charAt(getRandomInt(0, letters.length)),
	).join("");
}

function generateGroupCode(): string {
	return `${getRandomInt(1000, 9999)}-${randomLetters(4)}-${randomLetters(3)}`;
}

export const POST = auth(async function POST(request) {
	try {
		const body = await request.json();
		const validationData = AutoCreateGroupSchema.parse(body);
		const { editorId } = validationData;

		console.log("Auto-creating group for editor:", editorId);

		// Check if editor already has a group
		try {
			const existingGroup = await groupServices.getGroupByEditorId(editorId);
			if (existingGroup) {
				console.log("Editor already has a group:", existingGroup);
				return Response.json(
					{
						success: true,
						group: existingGroup,
						message: "Group already exists",
					},
					{ status: 200 },
				);
			}
		} catch (error) {
			// No existing group found, continue with creation
			console.log("No existing group found, creating new one");
		}

		// Generate unique group code
		let groupCode = generateGroupCode();
		let codeExists = true;
		let attempts = 0;
		const maxAttempts = 10;

		while (codeExists && attempts < maxAttempts) {
			try {
				await groupServices.getGroupBySharePassCode(groupCode);
				// If we get here, code exists, generate a new one
				groupCode = generateGroupCode();
				attempts++;
			} catch (error) {
				// Code doesn't exist, we can use it
				codeExists = false;
			}
		}

		if (attempts >= maxAttempts) {
			throw new Error("Failed to generate unique group code");
		}

		// Create the group
		const groupData = {
			editorId,
			groupName: `Editor Group - ${new Date().toLocaleDateString()}`,
			groupType: "author-group",
			sharedPasscode: groupCode,
		};

		await groupServices.createGroup(groupData);

		// Fetch the created group to return it
		const createdGroup = await groupServices.getGroupByEditorId(editorId);

		console.log("Group auto-created successfully:", createdGroup);

		return Response.json(
			{
				success: true,
				group: createdGroup,
				message: "Group created successfully",
			},
			{ status: 201 },
		);
	} catch (error) {
		console.error("Error auto-creating group:", error);

		if (error instanceof z.ZodError) {
			return Response.json(
				{ success: false, error: "Invalid request data", details: error.errors },
				{ status: 400 },
			);
		}

		if (error instanceof AppError) {
			return Response.json(
				{ success: false, error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json(
			{ success: false, error: "Internal server error" },
			{ status: 500 },
		);
	}
});
