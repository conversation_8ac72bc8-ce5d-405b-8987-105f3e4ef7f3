{"name": "@jefelabs-clients/uptownmedia-nyc", "version": "0.67.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "node --trace-warnings ./node_modules/.bin/next build && sh ./prepare-build.sh", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "storybook": "concurrently 'npm:watch:*'", "watch:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css --watch", "build:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css", "watch:storybook": "storybook dev -p 6006 - s ./public", "build-storybook": "npm run build:tailwind && storybook build ", "test-storybook": "test-storybook"}, "dependencies": {"@auth/dynamodb-adapter": "2.9.0", "@aws-sdk/client-dynamodb": "3.799.0", "@aws-sdk/lib-dynamodb": "3.799.0", "@heroui/button": "2.2.17", "@heroui/code": "2.2.12", "@heroui/input": "2.4.17", "@heroui/kbd": "2.2.13", "@heroui/link": "2.2.14", "@heroui/listbox": "2.3.16", "@heroui/navbar": "2.2.15", "@heroui/react": "2.7.6", "@heroui/snippet": "2.2.18", "@heroui/switch": "2.2.15", "@heroui/system": "2.4.13", "@heroui/theme": "2.4.13", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.21", "@skoolscout/verification-services": "^1.5.5", "bcryptjs": "^3.0.2", "clsx": "2.1.1", "concurrently": "^9.1.2", "framer-motion": "11.13.1", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-resources-to-backend": "^1.2.1", "intl-messageformat": "^10.5.0", "jsonwebtoken": "^9.0.2", "next": "15.0.4", "next-auth": "^5.0.0-beta.27", "next-themes": "^0.4.4", "react": "18.3.1", "react-cookie": "^8.0.1", "react-dom": "18.3.1", "react-haiku": "^2.2.0", "react-i18next": "^15.5.1", "react-tagcloud": "^2.3.3", "resend": "^4.4.1", "uuidv7": "^1.0.2"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@hookform/resolvers": "^5.0.1", "@iconify/react": "^6.0.0", "@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@storybook/addon-console": "^3.0.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/experimental-nextjs-vite": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "@vitest/browser": "^3.1.1", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-unused-imports": "4.1.4", "msw": "^2.7.4", "msw-storybook-addon": "^2.0.4", "playwright": "^1.51.1", "postcss": "8.4.49", "prettier": "3.3.3", "react-hook-form": "^7.56.1", "storybook": "^8.6.12", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3", "vitest": "^3.1.1", "zod": "^3.24.3", "zustand": "^5.0.3"}, "msw": {"workerDirectory": ["public"]}}